'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');
var typescript = require('../../../utils/typescript.js');

const classType = String;
const columns = {
  type: runtime.definePropType(Array),
  required: true
};
const column = {
  type: runtime.definePropType(Object)
};
const fixedDataType = {
  type: runtime.definePropType(Array)
};
const dataType = {
  ...fixedDataType,
  required: true
};
const expandColumnKey = String;
const expandKeys = {
  type: runtime.definePropType(Array),
  default: () => typescript.mutable([])
};
const requiredNumber = {
  type: Number,
  required: true
};
const rowKey = {
  type: runtime.definePropType([String, Number, Symbol]),
  default: "id"
};
const styleType = {
  type: runtime.definePropType(Object)
};

exports.classType = classType;
exports.column = column;
exports.columns = columns;
exports.dataType = dataType;
exports.expandColumnKey = expandColumnKey;
exports.expandKeys = expandKeys;
exports.fixedDataType = fixedDataType;
exports.requiredNumber = requiredNumber;
exports.rowKey = rowKey;
exports.styleType = styleType;
//# sourceMappingURL=common.js.map
