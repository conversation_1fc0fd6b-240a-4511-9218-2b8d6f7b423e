<template>
  <div class="implementation-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>采购实施</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            新建实施计划
          </el-button>
        </div>
      </template>
      
      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filterForm.projectStatus" placeholder="项目状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="准备中" value="PREPARING" />
              <el-option label="实施中" value="IMPLEMENTING" />
              <el-option label="已完成" value="COMPLETED" />
              <el-option label="已暂停" value="PAUSED" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterForm.implementationType" placeholder="实施类型" clearable>
              <el-option label="全部" value="" />
              <el-option label="公开招标" value="PUBLIC_TENDER" />
              <el-option label="邀请招标" value="INVITE_TENDER" />
              <el-option label="竞争性谈判" value="COMPETITIVE_NEGOTIATION" />
              <el-option label="单一来源" value="SINGLE_SOURCE" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 实施列表 -->
      <el-table :data="implementationList" style="width: 100%" v-loading="loading">
        <el-table-column prop="implementationNo" label="实施编号" width="160" />
        <el-table-column prop="projectName" label="项目名称" min-width="200" />
        <el-table-column prop="implementationType" label="实施类型" width="120">
          <template #default="{ row }">
            {{ getImplementationTypeText(row.implementationType) }}
          </template>
        </el-table-column>
        <el-table-column prop="responsiblePerson" label="负责人" width="100" />
        <el-table-column prop="plannedStartDate" label="计划开始" width="120" />
        <el-table-column prop="plannedEndDate" label="计划结束" width="120" />
        <el-table-column prop="actualProgress" label="实际进度" width="120">
          <template #default="{ row }">
            <el-progress :percentage="row.actualProgress" :stroke-width="8" />
          </template>
        </el-table-column>
        <el-table-column prop="implementationStatus" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.implementationStatus)">
              {{ getStatusText(row.implementationStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleManage(row)" v-if="row.implementationStatus === 'IMPLEMENTING'">
              管理
            </el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const filterForm = reactive({
  projectStatus: '',
  implementationType: '',
  dateRange: []
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const implementationList = ref([
  {
    id: 1,
    implementationNo: 'IMPL202506290001',
    projectName: '办公用品采购项目实施',
    implementationType: 'COMPETITIVE_NEGOTIATION',
    responsiblePerson: '王五',
    plannedStartDate: '2025-06-30',
    plannedEndDate: '2025-07-15',
    actualProgress: 65,
    implementationStatus: 'IMPLEMENTING',
    createTime: '2025-06-29 14:00:00'
  },
  {
    id: 2,
    implementationNo: 'IMPL202506290002',
    projectName: '计算机设备采购项目实施',
    implementationType: 'PUBLIC_TENDER',
    responsiblePerson: '赵六',
    plannedStartDate: '2025-07-01',
    plannedEndDate: '2025-07-30',
    actualProgress: 30,
    implementationStatus: 'PREPARING',
    createTime: '2025-06-29 15:00:00'
  },
  {
    id: 3,
    implementationNo: 'IMPL202506290003',
    projectName: '办公家具采购项目实施',
    implementationType: 'INVITE_TENDER',
    responsiblePerson: '孙七',
    plannedStartDate: '2025-06-25',
    plannedEndDate: '2025-07-10',
    actualProgress: 100,
    implementationStatus: 'COMPLETED',
    createTime: '2025-06-25 10:00:00'
  }
])

// 方法
const getImplementationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'PUBLIC_TENDER': '公开招标',
    'INVITE_TENDER': '邀请招标',
    'COMPETITIVE_NEGOTIATION': '竞争性谈判',
    'SINGLE_SOURCE': '单一来源'
  }
  return typeMap[type] || type
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'PREPARING': 'info',
    'IMPLEMENTING': 'warning',
    'COMPLETED': 'success',
    'PAUSED': 'danger'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'PREPARING': '准备中',
    'IMPLEMENTING': '实施中',
    'COMPLETED': '已完成',
    'PAUSED': '已暂停'
  }
  return textMap[status] || status
}

const handleSearch = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    projectStatus: '',
    implementationType: '',
    dateRange: []
  })
}

const handleView = (row: any) => {
  ElMessage.info(`查看实施详情: ${row.implementationNo}`)
}

const handleManage = (row: any) => {
  ElMessage.info(`管理实施项目: ${row.implementationNo}`)
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑实施计划: ${row.implementationNo}`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  // 重新加载数据
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  // 重新加载数据
}

onMounted(() => {
  pagination.total = implementationList.value.length
})
</script>

<style lang="scss" scoped>
.implementation-management {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .filter-section {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .pagination-section {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
