<template>
  <div class="tracking-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>采购跟踪</span>
          <el-button type="primary">
            <el-icon><Plus /></el-icon>
            添加跟踪记录
          </el-button>
        </div>
      </template>
      
      <!-- 统计概览 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <el-icon size="24"><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.totalProjects }}</div>
                <div class="stat-label">跟踪项目</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <el-icon size="24"><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.ongoingProjects }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <el-icon size="24"><SuccessFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.completedProjects }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <el-icon size="24"><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.delayedProjects }}</div>
                <div class="stat-label">延期项目</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input v-model="filterForm.projectName" placeholder="项目名称" clearable>
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterForm.trackingStatus" placeholder="跟踪状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="正常" value="NORMAL" />
              <el-option label="延期" value="DELAYED" />
              <el-option label="风险" value="RISK" />
              <el-option label="完成" value="COMPLETED" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterForm.responsiblePerson" placeholder="负责人" clearable>
              <el-option label="全部" value="" />
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="王五" value="王五" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </div>
      
      <!-- 跟踪列表 -->
      <el-table :data="trackingList" style="width: 100%" v-loading="loading">
        <el-table-column prop="trackingNo" label="跟踪编号" width="160" />
        <el-table-column prop="projectName" label="项目名称" min-width="200" />
        <el-table-column prop="responsiblePerson" label="负责人" width="100" />
        <el-table-column prop="currentPhase" label="当前阶段" width="120" />
        <el-table-column prop="progress" label="完成进度" width="120">
          <template #default="{ row }">
            <el-progress :percentage="row.progress" :stroke-width="8" />
          </template>
        </el-table-column>
        <el-table-column prop="plannedEndDate" label="计划完成" width="120" />
        <el-table-column prop="trackingStatus" label="跟踪状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.trackingStatus)">
              {{ getStatusText(row.trackingStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdateTime" label="最后更新" width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="success" size="small" @click="handleUpdate(row)">更新</el-button>
            <el-button type="warning" size="small" @click="handleReport(row)">报告</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const loading = ref(false)
const stats = reactive({
  totalProjects: 15,
  ongoingProjects: 8,
  completedProjects: 5,
  delayedProjects: 2
})

const filterForm = reactive({
  projectName: '',
  trackingStatus: '',
  responsiblePerson: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const trackingList = ref([
  {
    id: 1,
    trackingNo: 'TRACK202506290001',
    projectName: '办公用品采购项目',
    responsiblePerson: '张三',
    currentPhase: '供应商评估',
    progress: 75,
    plannedEndDate: '2025-07-15',
    trackingStatus: 'NORMAL',
    lastUpdateTime: '2025-06-29 16:30:00'
  },
  {
    id: 2,
    trackingNo: 'TRACK202506290002',
    projectName: '计算机设备采购项目',
    responsiblePerson: '李四',
    currentPhase: '合同签订',
    progress: 90,
    plannedEndDate: '2025-07-30',
    trackingStatus: 'NORMAL',
    lastUpdateTime: '2025-06-29 15:45:00'
  },
  {
    id: 3,
    trackingNo: 'TRACK202506290003',
    projectName: '办公家具采购项目',
    responsiblePerson: '王五',
    currentPhase: '验收交付',
    progress: 45,
    plannedEndDate: '2025-07-10',
    trackingStatus: 'DELAYED',
    lastUpdateTime: '2025-06-29 14:20:00'
  },
  {
    id: 4,
    trackingNo: 'TRACK202506290004',
    projectName: '清洁用品采购项目',
    responsiblePerson: '赵六',
    currentPhase: '项目完成',
    progress: 100,
    plannedEndDate: '2025-06-28',
    trackingStatus: 'COMPLETED',
    lastUpdateTime: '2025-06-28 17:00:00'
  }
])

// 方法
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'NORMAL': 'success',
    'DELAYED': 'warning',
    'RISK': 'danger',
    'COMPLETED': 'info'
  }
  return colorMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'NORMAL': '正常',
    'DELAYED': '延期',
    'RISK': '风险',
    'COMPLETED': '完成'
  }
  return textMap[status] || status
}

const handleSearch = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    loading.value = false
    ElMessage.success('搜索完成')
  }, 1000)
}

const handleReset = () => {
  Object.assign(filterForm, {
    projectName: '',
    trackingStatus: '',
    responsiblePerson: ''
  })
}

const handleView = (row: any) => {
  ElMessage.info(`查看跟踪详情: ${row.trackingNo}`)
}

const handleUpdate = (row: any) => {
  ElMessage.info(`更新跟踪状态: ${row.trackingNo}`)
}

const handleReport = (row: any) => {
  ElMessage.info(`生成跟踪报告: ${row.trackingNo}`)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  // 重新加载数据
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  // 重新加载数据
}

onMounted(() => {
  pagination.total = trackingList.value.length
})
</script>

<style lang="scss" scoped>
.tracking-management {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .stats-section {
    margin-bottom: 24px;
    
    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 16px;
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 28px;
          font-weight: 700;
          color: #1e293b;
          line-height: 1;
          margin-bottom: 4px;
        }
        
        .stat-label {
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }
    }
  }
  
  .filter-section {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
  }
  
  .pagination-section {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}
</style>
