<template>
  <div class="dashboard">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ userStore.userInfo?.realName || '系统管理员' }}！</h1>
          <p>今天是 {{ currentDate }}，祝您工作愉快</p>
        </div>
        <div class="welcome-illustration">
          <div class="floating-icon">🎯</div>
          <div class="floating-icon delay-1">📊</div>
          <div class="floating-icon delay-2">💼</div>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <h2 class="section-title">
        <el-icon><TrendCharts /></el-icon>
        数据概览
      </h2>
      <el-row :gutter="24">
        <el-col :span="6" v-for="(item, index) in statsCards" :key="item.title">
          <div class="stats-card" :class="`stats-card-${index + 1}`">
            <div class="stats-background"></div>
            <div class="stats-content">
              <div class="stats-info">
                <div class="stats-number">{{ item.value }}</div>
                <div class="stats-title">{{ item.title }}</div>
                <div class="stats-trend">
                  <el-icon><ArrowUp /></el-icon>
                  <span>+12%</span>
                </div>
              </div>
              <div class="stats-icon" :style="{ background: item.gradient }">
                <el-icon :size="32">
                  <component :is="item.icon" />
                </el-icon>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 功能区域 -->
    <el-row :gutter="24" class="function-section">
      <!-- 快捷操作 -->
      <el-col :span="14">
        <div class="quick-actions-card">
          <div class="card-header">
            <h3>
              <el-icon><Lightning /></el-icon>
              快捷操作
            </h3>
            <span class="card-subtitle">常用功能快速入口</span>
          </div>
          <div class="quick-actions-grid">
            <div
              v-for="action in quickActions"
              :key="action.name"
              class="action-item"
              :class="`action-${action.type}`"
              @click="handleQuickAction(action.action)"
            >
              <div class="action-icon">
                <el-icon :size="28">
                  <component :is="action.icon" />
                </el-icon>
              </div>
              <div class="action-content">
                <div class="action-title">{{ action.name }}</div>
                <div class="action-desc">{{ action.description }}</div>
              </div>
              <div class="action-arrow">
                <el-icon><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 系统信息 -->
      <el-col :span="10">
        <div class="system-info-card">
          <div class="card-header">
            <h3>
              <el-icon><Monitor /></el-icon>
              系统信息
            </h3>
            <span class="card-subtitle">当前状态概览</span>
          </div>
          <div class="system-info-content">
            <div class="user-profile">
              <el-avatar :size="60" class="user-avatar">
                {{ (userStore.userInfo?.realName || '系统管理员').charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="user-name">{{ userStore.userInfo?.realName || '系统管理员' }}</div>
                <div class="user-role">{{ userStore.userInfo?.position || '信息中心' }}</div>
              </div>
            </div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-icon">🏢</div>
                <div class="info-content">
                  <div class="info-label">所属部门</div>
                  <div class="info-value">{{ userStore.userInfo?.department || '信息中心' }}</div>
                </div>
              </div>
              <div class="info-item">
                <div class="info-icon">🕐</div>
                <div class="info-content">
                  <div class="info-label">最后登录</div>
                  <div class="info-value">{{ formatTime(userStore.userInfo?.lastLoginTime) }}</div>
                </div>
              </div>
              <div class="info-item">
                <div class="info-icon">⚙️</div>
                <div class="info-content">
                  <div class="info-label">系统版本</div>
                  <div class="info-value">v1.0.0</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 监督预警区域 -->
    <div class="warning-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><Warning /></el-icon>
          监督预警
        </h2>
        <el-button type="warning" class="view-all-btn">
          <el-icon><Monitor /></el-icon>
          监控中心
        </el-button>
      </div>

      <el-row :gutter="24">
        <el-col :span="8">
          <div class="warning-card high-risk">
            <div class="warning-header">
              <div class="warning-icon">
                <el-icon><WarnTriangleFilled /></el-icon>
              </div>
              <div class="warning-info">
                <div class="warning-title">高风险预警</div>
                <div class="warning-count">{{ warningStats.highRisk }}</div>
              </div>
            </div>
            <div class="warning-desc">需要立即处理的高风险事项</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="warning-card medium-risk">
            <div class="warning-header">
              <div class="warning-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="warning-info">
                <div class="warning-title">中风险预警</div>
                <div class="warning-count">{{ warningStats.mediumRisk }}</div>
              </div>
            </div>
            <div class="warning-desc">需要关注的中等风险事项</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="warning-card compliance">
            <div class="warning-header">
              <div class="warning-icon">
                <el-icon><Shield /></el-icon>
              </div>
              <div class="warning-info">
                <div class="warning-title">合规检查</div>
                <div class="warning-count">{{ warningStats.compliance }}</div>
              </div>
            </div>
            <div class="warning-desc">合规性检查待处理事项</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 统计分析区域 -->
    <div class="analytics-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><TrendCharts /></el-icon>
          统计分析
        </h2>
        <el-button type="success" class="view-all-btn">
          <el-icon><DataAnalysis /></el-icon>
          分析中心
        </el-button>
      </div>

      <el-row :gutter="24">
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>采购趋势分析</h3>
              <el-select v-model="chartPeriod" size="small">
                <el-option label="近7天" value="7d" />
                <el-option label="近30天" value="30d" />
                <el-option label="近3个月" value="3m" />
              </el-select>
            </div>
            <div class="chart-content">
              <div class="chart-placeholder">
                📈 采购金额趋势图
                <div class="chart-data">
                  <div class="data-point">
                    <span class="label">本月采购额</span>
                    <span class="value">¥2,580,000</span>
                  </div>
                  <div class="data-point">
                    <span class="label">环比增长</span>
                    <span class="value positive">+15.2%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>供应商分布</h3>
              <el-button type="text" size="small">详细报告</el-button>
            </div>
            <div class="chart-content">
              <div class="chart-placeholder">
                🥧 供应商类型分布图
                <div class="chart-data">
                  <div class="data-point">
                    <span class="label">合作供应商</span>
                    <span class="value">156家</span>
                  </div>
                  <div class="data-point">
                    <span class="label">活跃度</span>
                    <span class="value positive">85.6%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 待办事项 -->
    <div class="todo-section">
      <div class="section-header">
        <h2 class="section-title">
          <el-icon><List /></el-icon>
          待办事项
        </h2>
        <el-button type="primary" class="view-all-btn">
          <el-icon><View /></el-icon>
          查看全部
        </el-button>
      </div>

      <div class="todo-cards">
        <div
          v-for="todo in todoList"
          :key="todo.id"
          class="todo-card"
          :class="`priority-${todo.priority}`"
          @click="handleTodo(todo)"
        >
          <div class="todo-header">
            <div class="todo-type">
              <el-tag :type="getTypeColor(todo.type)" size="small">{{ todo.type }}</el-tag>
            </div>
            <div class="todo-priority">
              <el-tag :type="getPriorityColor(todo.priority)" size="small">{{ todo.priority }}</el-tag>
            </div>
          </div>

          <div class="todo-content">
            <h4 class="todo-title">{{ todo.title }}</h4>
            <div class="todo-meta">
              <div class="todo-deadline">
                <el-icon><Clock /></el-icon>
                <span>{{ todo.deadline }}</span>
              </div>
            </div>
          </div>

          <div class="todo-actions">
            <el-button type="primary" size="small" class="handle-btn">
              <el-icon><Check /></el-icon>
              处理
            </el-button>
          </div>

          <div class="todo-indicator"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

// 当前日期
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日 dddd')
})

// 统计卡片数据
const statsCards = ref([
  {
    title: '待处理需求',
    value: '12',
    icon: 'Document',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    title: '进行中项目',
    value: '8',
    icon: 'Folder',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    title: '待签合同',
    value: '5',
    icon: 'DocumentCopy',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    title: '待付款项',
    value: '3',
    icon: 'Money',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  }
])

// 快捷操作
const quickActions = ref([
  {
    name: '新建需求',
    type: 'primary',
    icon: 'Plus',
    action: 'createRequirement',
    description: '创建新的采购需求'
  },
  {
    name: '创建项目',
    type: 'success',
    icon: 'FolderAdd',
    action: 'createProject',
    description: '启动新的采购项目'
  },
  {
    name: '供应商管理',
    type: 'warning',
    icon: 'User',
    action: 'manageSupplier',
    description: '管理供应商信息'
  },
  {
    name: '合同管理',
    type: 'info',
    icon: 'Document',
    action: 'manageContract',
    description: '处理合同相关事务'
  }
])

// 待办事项
const todoList = ref([
  {
    id: 1,
    title: '办公用品采购需求审批',
    type: '需求审批',
    priority: '高',
    deadline: '2025-06-30 18:00'
  },
  {
    id: 2,
    title: '计算机设备采购项目验收',
    type: '项目验收',
    priority: '中',
    deadline: '2025-07-01 12:00'
  },
  {
    id: 3,
    title: '办公家具供应商报价评审',
    type: '报价评审',
    priority: '中',
    deadline: '2025-07-02 15:00'
  },
  {
    id: 4,
    title: '软件服务合同付款审批',
    type: '付款审批',
    priority: '高',
    deadline: '2025-06-29 17:00'
  }
])

// 监督预警统计数据
const warningStats = ref({
  highRisk: 3,      // 高风险预警数量
  mediumRisk: 8,    // 中风险预警数量
  compliance: 2     // 合规检查待处理数量
})

// 统计分析数据
const chartPeriod = ref('30d')

// 格式化时间
const formatTime = (time?: string) => {
  if (!time) return '-'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    '需求审批': 'primary',
    '项目验收': 'success',
    '报价评审': 'warning',
    '付款审批': 'danger'
  }
  return colorMap[type] || 'info'
}

// 获取优先级颜色
const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  }
  return colorMap[priority] || 'info'
}

// 处理快捷操作
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'createRequirement':
      router.push('/procurement/requirement')
      break
    case 'createProject':
      router.push('/procurement/project')
      break
    case 'manageSupplier':
      ElMessage.info('供应商管理功能开发中...')
      break
    case 'manageContract':
      ElMessage.info('合同管理功能开发中...')
      break
    default:
      ElMessage.info('功能开发中...')
  }
}

// 处理待办事项
const handleTodo = (todo: any) => {
  ElMessage.info(`处理待办事项：${todo.title}`)
}

onMounted(() => {
  // 可以在这里加载实际的统计数据
})
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 0;

  // 欢迎横幅
  .welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .welcome-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      z-index: 1;
    }

    .welcome-text {
      h1 {
        font-size: 32px;
        font-weight: 700;
        color: #fff;
        margin: 0 0 12px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        font-weight: 400;
      }
    }

    .welcome-illustration {
      display: flex;
      gap: 20px;

      .floating-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        backdrop-filter: blur(10px);
        animation: float 3s ease-in-out infinite;

        &.delay-1 {
          animation-delay: 0.5s;
        }

        &.delay-2 {
          animation-delay: 1s;
        }
      }
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  // 统计卡片区域
  .stats-section {
    margin-bottom: 32px;

    .section-title {
      font-size: 24px;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      gap: 12px;

      .el-icon {
        color: #667eea;
        font-size: 28px;
      }
    }
  }

  .stats-card {
    background: #fff;
    border-radius: 20px;
    padding: 32px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(226, 232, 240, 0.8);
    height: 160px;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .stats-background {
      position: absolute;
      top: 0;
      right: 0;
      width: 120px;
      height: 120px;
      background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
      border-radius: 50%;
      transform: translate(30px, -30px);
    }

    .stats-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      position: relative;
      z-index: 1;
    }

    .stats-info {
      .stats-number {
        font-size: 48px;
        font-weight: 800;
        color: #1e293b;
        margin-bottom: 8px;
        line-height: 1;
      }

      .stats-title {
        font-size: 16px;
        color: #64748b;
        margin-bottom: 12px;
        font-weight: 500;
      }

      .stats-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #10b981;
        font-size: 14px;
        font-weight: 600;

        .el-icon {
          font-size: 16px;
        }
      }
    }

    .stats-icon {
      width: 80px;
      height: 80px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

      .el-icon {
        font-size: 32px;
      }
    }

    // 不同卡片的特殊样式
    &.stats-card-1 {
      .stats-background {
        background: radial-gradient(circle, rgba(102, 126, 234, 0.15) 0%, transparent 70%);
      }
    }

    &.stats-card-2 {
      .stats-background {
        background: radial-gradient(circle, rgba(240, 147, 251, 0.15) 0%, transparent 70%);
      }
    }

    &.stats-card-3 {
      .stats-background {
        background: radial-gradient(circle, rgba(79, 172, 254, 0.15) 0%, transparent 70%);
      }
    }

    &.stats-card-4 {
      .stats-background {
        background: radial-gradient(circle, rgba(67, 233, 123, 0.15) 0%, transparent 70%);
      }
    }
  }

  // 功能区域
  .function-section {
    margin-bottom: 32px;
  }

  .quick-actions-card, .system-info-card {
    background: #fff;
    border-radius: 20px;
    padding: 32px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.08);
    }

    .card-header {
      margin-bottom: 24px;

      h3 {
        font-size: 20px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 4px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          color: #667eea;
          font-size: 24px;
        }
      }

      .card-subtitle {
        font-size: 14px;
        color: #64748b;
        font-weight: 400;
      }
    }
  }

  .quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;

    .action-item {
      display: flex;
      align-items: center;
      padding: 20px;
      border-radius: 16px;
      background: #f8fafc;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
        border-color: #e2e8f0;
        transform: translateX(8px);
      }

      .action-icon {
        width: 56px;
        height: 56px;
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        color: #fff;

        .el-icon {
          font-size: 28px;
        }
      }

      .action-content {
        flex: 1;

        .action-title {
          font-size: 16px;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .action-desc {
          font-size: 14px;
          color: #64748b;
        }
      }

      .action-arrow {
        color: #94a3b8;
        transition: all 0.3s ease;

        .el-icon {
          font-size: 20px;
        }
      }

      &:hover .action-arrow {
        color: #667eea;
        transform: translateX(4px);
      }

      &.action-primary .action-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.action-success .action-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.action-warning .action-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.action-info .action-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
  }

  // 系统信息卡片
  .system-info-content {
    .user-profile {
      display: flex;
      align-items: center;
      padding: 24px;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-radius: 16px;
      margin-bottom: 24px;

      .user-avatar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        font-weight: 700;
        font-size: 24px;
        margin-right: 16px;
      }

      .user-details {
        .user-name {
          font-size: 18px;
          font-weight: 700;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .user-role {
          font-size: 14px;
          color: #64748b;
        }
      }
    }

    .info-grid {
      display: grid;
      gap: 16px;

      .info-item {
        display: flex;
        align-items: center;
        padding: 16px;
        background: #f8fafc;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
          transform: translateX(4px);
        }

        .info-icon {
          font-size: 20px;
          margin-right: 12px;
        }

        .info-content {
          .info-label {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 2px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .info-value {
            font-size: 14px;
            color: #1e293b;
            font-weight: 600;
          }
        }
      }
    }
  }

  // 待办事项区域
  .todo-section {
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;

      .section-title {
        font-size: 24px;
        font-weight: 700;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          color: #667eea;
          font-size: 28px;
        }
      }

      .view-all-btn {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;

        .el-icon {
          margin-right: 8px;
        }
      }
    }

    .todo-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;

      .todo-card {
        background: #fff;
        border-radius: 16px;
        padding: 24px;
        border: 2px solid transparent;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        overflow: hidden;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
          border-color: #e2e8f0;
        }

        .todo-indicator {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          border-radius: 0 2px 2px 0;
        }

        &.priority-高 .todo-indicator {
          background: linear-gradient(180deg, #f56565 0%, #e53e3e 100%);
        }

        &.priority-中 .todo-indicator {
          background: linear-gradient(180deg, #ed8936 0%, #dd6b20 100%);
        }

        &.priority-低 .todo-indicator {
          background: linear-gradient(180deg, #48bb78 0%, #38a169 100%);
        }

        .todo-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;

          .el-tag {
            border-radius: 8px;
            font-weight: 500;
          }
        }

        .todo-content {
          margin-bottom: 20px;

          .todo-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 12px 0;
            line-height: 1.4;
          }

          .todo-meta {
            .todo-deadline {
              display: flex;
              align-items: center;
              gap: 6px;
              color: #64748b;
              font-size: 14px;

              .el-icon {
                font-size: 16px;
              }
            }
          }
        }

        .todo-actions {
          .handle-btn {
            width: 100%;
            border-radius: 10px;
            font-weight: 600;

            .el-icon {
              margin-right: 6px;
            }
          }
        }
      }
    }
  }

  // 监督预警区域
  .warning-section {
    margin-bottom: 32px;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;

      .section-title {
        font-size: 24px;
        font-weight: 700;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          color: #f59e0b;
          font-size: 28px;
        }
      }

      .view-all-btn {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;

        .el-icon {
          margin-right: 8px;
        }
      }
    }

    .warning-card {
      background: #fff;
      border-radius: 16px;
      padding: 24px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
      }

      .warning-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .warning-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          color: #fff;

          .el-icon {
            font-size: 24px;
          }
        }

        .warning-info {
          .warning-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
          }

          .warning-count {
            font-size: 32px;
            font-weight: 800;
            line-height: 1;
          }
        }
      }

      .warning-desc {
        font-size: 14px;
        color: #64748b;
        line-height: 1.4;
      }

      &.high-risk {
        border-color: rgba(239, 68, 68, 0.2);

        .warning-icon {
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .warning-count {
          color: #ef4444;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
        }
      }

      &.medium-risk {
        border-color: rgba(245, 158, 11, 0.2);

        .warning-icon {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .warning-count {
          color: #f59e0b;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
        }
      }

      &.compliance {
        border-color: rgba(59, 130, 246, 0.2);

        .warning-icon {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .warning-count {
          color: #3b82f6;
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
        }
      }
    }
  }

  // 统计分析区域
  .analytics-section {
    margin-bottom: 32px;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;

      .section-title {
        font-size: 24px;
        font-weight: 700;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          color: #10b981;
          font-size: 28px;
        }
      }

      .view-all-btn {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;

        .el-icon {
          margin-right: 8px;
        }
      }
    }

    .chart-card {
      background: #fff;
      border-radius: 16px;
      padding: 24px;
      border: 1px solid rgba(226, 232, 240, 0.8);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.08);
        transform: translateY(-2px);
      }

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }

        .el-select {
          width: 120px;
        }

        .el-button {
          padding: 8px 16px;
          font-size: 14px;
        }
      }

      .chart-content {
        .chart-placeholder {
          height: 200px;
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-radius: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-size: 48px;
          color: #64748b;
          position: relative;

          .chart-data {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;

            .data-point {
              text-align: center;

              .label {
                display: block;
                font-size: 12px;
                color: #64748b;
                margin-bottom: 4px;
              }

              .value {
                display: block;
                font-size: 16px;
                font-weight: 600;
                color: #1e293b;

                &.positive {
                  color: #10b981;
                }

                &.negative {
                  color: #ef4444;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
