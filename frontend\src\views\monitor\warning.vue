<template>
  <div class="warning-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Warning /></el-icon>
          预警管理
        </h1>
        <p class="page-subtitle">监控系统运行状态，及时发现和处理各类预警信息</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="refreshWarnings">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" @click="exportWarnings">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>

    <!-- 预警统计卡片 -->
    <div class="warning-stats">
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="stat-card critical">
            <div class="stat-icon">
              <el-icon><WarnTriangleFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ warningStats.critical }}</div>
              <div class="stat-label">紧急预警</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card high">
            <div class="stat-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ warningStats.high }}</div>
              <div class="stat-label">高风险预警</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card medium">
            <div class="stat-icon">
              <el-icon><InfoFilled /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ warningStats.medium }}</div>
              <div class="stat-label">中风险预警</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card low">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ warningStats.low }}</div>
              <div class="stat-label">低风险预警</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 预警筛选 -->
    <div class="warning-filters">
      <el-card>
        <el-form :model="filterForm" inline>
          <el-form-item label="预警类型">
            <el-select v-model="filterForm.type" placeholder="选择预警类型" clearable>
              <el-option label="付款时限预警" value="payment" />
              <el-option label="合同到期预警" value="contract" />
              <el-option label="预算超支预警" value="budget" />
              <el-option label="合规性预警" value="compliance" />
              <el-option label="双人制预警" value="dual_person" />
            </el-select>
          </el-form-item>
          <el-form-item label="预警级别">
            <el-select v-model="filterForm.level" placeholder="选择预警级别" clearable>
              <el-option label="紧急" value="critical" />
              <el-option label="高风险" value="high" />
              <el-option label="中风险" value="medium" />
              <el-option label="低风险" value="low" />
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态">
            <el-select v-model="filterForm.status" placeholder="选择处理状态" clearable>
              <el-option label="未处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已处理" value="resolved" />
              <el-option label="已忽略" value="ignored" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchWarnings">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="resetFilters">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 预警列表 -->
    <div class="warning-list">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>预警列表</span>
            <div class="header-actions">
              <el-button size="small" @click="batchProcess">批量处理</el-button>
              <el-button size="small" type="danger" @click="batchIgnore">批量忽略</el-button>
            </div>
          </div>
        </template>
        
        <el-table
          :data="warningList"
          v-loading="loading"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="预警ID" width="100" />
          <el-table-column prop="type" label="预警类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeColor(row.type)">{{ getTypeName(row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="预警级别" width="100">
            <template #default="{ row }">
              <el-tag :type="getLevelColor(row.level)">{{ getLevelName(row.level) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="title" label="预警内容" min-width="200" />
          <el-table-column prop="relatedProject" label="关联项目" width="150" />
          <el-table-column prop="createTime" label="预警时间" width="180" />
          <el-table-column prop="status" label="处理状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusColor(row.status)">{{ getStatusName(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" type="primary" @click="handleWarning(row)">
                处理
              </el-button>
              <el-button size="small" type="info" @click="viewDetails(row)">
                详情
              </el-button>
              <el-button size="small" type="warning" @click="ignoreWarning(row)">
                忽略
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 预警处理对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      title="处理预警"
      width="600px"
    >
      <el-form :model="processForm" label-width="100px">
        <el-form-item label="处理方式">
          <el-radio-group v-model="processForm.action">
            <el-radio label="resolve">立即解决</el-radio>
            <el-radio label="assign">分配处理</el-radio>
            <el-radio label="defer">延期处理</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理人员" v-if="processForm.action === 'assign'">
          <el-select v-model="processForm.assignee" placeholder="选择处理人员">
            <el-option label="张三" value="zhangsan" />
            <el-option label="李四" value="lisi" />
            <el-option label="王五" value="wangwu" />
          </el-select>
        </el-form-item>
        <el-form-item label="预计完成时间" v-if="processForm.action === 'defer'">
          <el-date-picker
            v-model="processForm.dueDate"
            type="datetime"
            placeholder="选择预计完成时间"
          />
        </el-form-item>
        <el-form-item label="处理说明">
          <el-input
            v-model="processForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入处理说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitProcess">确认处理</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 预警统计数据
const warningStats = ref({
  critical: 2,
  high: 5,
  medium: 12,
  low: 8
})

// 筛选表单
const filterForm = ref({
  type: '',
  level: '',
  status: '',
  dateRange: []
})

// 预警列表数据
const warningList = ref([
  {
    id: 'W001',
    type: 'payment',
    level: 'critical',
    title: '办公用品采购项目付款即将超期（剩余1天）',
    relatedProject: 'P2024001',
    createTime: '2025-06-29 09:30:00',
    status: 'pending'
  },
  {
    id: 'W002',
    type: 'contract',
    level: 'high',
    title: '计算机设备采购合同即将到期',
    relatedProject: 'P2024002',
    createTime: '2025-06-29 10:15:00',
    status: 'processing'
  },
  {
    id: 'W003',
    type: 'budget',
    level: 'medium',
    title: '办公家具采购预算使用率超过90%',
    relatedProject: 'P2024003',
    createTime: '2025-06-29 11:20:00',
    status: 'pending'
  },
  {
    id: 'W004',
    type: 'compliance',
    level: 'high',
    title: '软件服务采购项目双人制执行异常',
    relatedProject: 'P2024004',
    createTime: '2025-06-29 14:45:00',
    status: 'pending'
  }
])

const loading = ref(false)
const selectedWarnings = ref([])

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 4
})

// 处理对话框
const processDialogVisible = ref(false)
const processForm = ref({
  action: 'resolve',
  assignee: '',
  dueDate: '',
  remark: ''
})

// 获取预警类型颜色
const getTypeColor = (type: string) => {
  const colors = {
    payment: 'danger',
    contract: 'warning',
    budget: 'info',
    compliance: 'primary',
    dual_person: 'success'
  }
  return colors[type] || 'info'
}

// 获取预警类型名称
const getTypeName = (type: string) => {
  const names = {
    payment: '付款时限',
    contract: '合同到期',
    budget: '预算超支',
    compliance: '合规性',
    dual_person: '双人制'
  }
  return names[type] || type
}

// 获取预警级别颜色
const getLevelColor = (level: string) => {
  const colors = {
    critical: 'danger',
    high: 'warning',
    medium: 'info',
    low: 'success'
  }
  return colors[level] || 'info'
}

// 获取预警级别名称
const getLevelName = (level: string) => {
  const names = {
    critical: '紧急',
    high: '高风险',
    medium: '中风险',
    low: '低风险'
  }
  return names[level] || level
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colors = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    ignored: 'info'
  }
  return colors[status] || 'info'
}

// 获取状态名称
const getStatusName = (status: string) => {
  const names = {
    pending: '未处理',
    processing: '处理中',
    resolved: '已处理',
    ignored: '已忽略'
  }
  return names[status] || status
}

// 刷新预警数据
const refreshWarnings = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

// 导出预警报告
const exportWarnings = () => {
  ElMessage.success('预警报告导出成功')
}

// 搜索预警
const searchWarnings = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    loading.value = false
    ElMessage.success('查询完成')
  }, 500)
}

// 重置筛选条件
const resetFilters = () => {
  filterForm.value = {
    type: '',
    level: '',
    status: '',
    dateRange: []
  }
  searchWarnings()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedWarnings.value = selection
}

// 批量处理
const batchProcess = () => {
  if (selectedWarnings.value.length === 0) {
    ElMessage.warning('请选择要处理的预警')
    return
  }
  ElMessage.success(`批量处理 ${selectedWarnings.value.length} 条预警`)
}

// 批量忽略
const batchIgnore = () => {
  if (selectedWarnings.value.length === 0) {
    ElMessage.warning('请选择要忽略的预警')
    return
  }
  ElMessageBox.confirm(
    `确定要忽略选中的 ${selectedWarnings.value.length} 条预警吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('批量忽略成功')
  })
}

// 处理预警
const handleWarning = (row: any) => {
  processDialogVisible.value = true
  processForm.value = {
    action: 'resolve',
    assignee: '',
    dueDate: '',
    remark: ''
  }
}

// 查看详情
const viewDetails = (row: any) => {
  ElMessage.info(`查看预警 ${row.id} 的详细信息`)
}

// 忽略预警
const ignoreWarning = (row: any) => {
  ElMessageBox.confirm(
    `确定要忽略预警 "${row.title}" 吗？`,
    '确认操作',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('预警已忽略')
  })
}

// 提交处理
const submitProcess = () => {
  ElMessage.success('预警处理成功')
  processDialogVisible.value = false
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  searchWarnings()
}

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  searchWarnings()
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.warning-management {
  padding: 24px;

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;

    .header-content {
      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          color: #f59e0b;
          font-size: 32px;
        }
      }

      .page-subtitle {
        font-size: 16px;
        color: #64748b;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .warning-stats {
    margin-bottom: 32px;

    .stat-card {
      background: #fff;
      border-radius: 16px;
      padding: 24px;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
      }

      .stat-icon {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;

        .el-icon {
          font-size: 28px;
          color: #fff;
        }
      }

      .stat-content {
        .stat-number {
          font-size: 36px;
          font-weight: 800;
          line-height: 1;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }

      &.critical {
        border-color: rgba(239, 68, 68, 0.2);

        .stat-icon {
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .stat-number {
          color: #ef4444;
        }
      }

      &.high {
        border-color: rgba(245, 158, 11, 0.2);

        .stat-icon {
          background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .stat-number {
          color: #f59e0b;
        }
      }

      &.medium {
        border-color: rgba(59, 130, 246, 0.2);

        .stat-icon {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .stat-number {
          color: #3b82f6;
        }
      }

      &.low {
        border-color: rgba(16, 185, 129, 0.2);

        .stat-icon {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .stat-number {
          color: #10b981;
        }
      }
    }
  }

  .warning-filters {
    margin-bottom: 24px;

    .el-card {
      border-radius: 16px;
      border: 1px solid rgba(226, 232, 240, 0.8);
    }
  }

  .warning-list {
    .el-card {
      border-radius: 16px;
      border: 1px solid rgba(226, 232, 240, 0.8);
    }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-actions {
        display: flex;
        gap: 8px;
      }
    }

    .pagination-wrapper {
      margin-top: 24px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
