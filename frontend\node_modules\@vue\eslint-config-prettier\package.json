{"name": "@vue/eslint-config-prettier", "version": "9.0.0", "description": "eslint-config-prettier for Vue", "main": "index.js", "files": ["index.js", "skip-formatting.js"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-config-prettier.git"}, "keywords": ["vue", "cli", "eslint", "prettier"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/eslint-config-prettier/issues"}, "homepage": "https://github.com/vuejs/eslint-config-prettier#readme", "dependencies": {"eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "devDependencies": {"eslint": "^8.45.0", "prettier": "^3.0.0"}, "peerDependencies": {"eslint": ">= 8.0.0", "prettier": ">= 3.0.0"}, "eslintConfig": {"extends": ["./index.js"]}, "scripts": {"lint": "eslint *.js --fix"}}