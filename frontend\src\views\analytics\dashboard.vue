<template>
  <div class="analytics-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><DataAnalysis /></el-icon>
          数据分析
        </h1>
        <p class="page-subtitle">全面分析采购业务数据，为决策提供科学依据</p>
      </div>
      <div class="header-actions">
        <el-select v-model="timeRange" placeholder="选择时间范围">
          <el-option label="近7天" value="7d" />
          <el-option label="近30天" value="30d" />
          <el-option label="近3个月" value="3m" />
          <el-option label="近6个月" value="6m" />
          <el-option label="近1年" value="1y" />
        </el-select>
        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">¥2,580,000</div>
              <div class="metric-label">总采购金额</div>
              <div class="metric-trend positive">
                <el-icon><TrendCharts /></el-icon>
                +15.2%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">156</div>
              <div class="metric-label">采购项目数</div>
              <div class="metric-trend positive">
                <el-icon><TrendCharts /></el-icon>
                +8.7%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">89</div>
              <div class="metric-label">合作供应商</div>
              <div class="metric-trend positive">
                <el-icon><TrendCharts /></el-icon>
                +12.5%
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card">
            <div class="metric-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">95.6%</div>
              <div class="metric-label">按时付款率</div>
              <div class="metric-trend positive">
                <el-icon><TrendCharts /></el-icon>
                +2.3%
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <!-- 采购趋势分析 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>采购金额趋势</h3>
                <el-button type="text" size="small">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                📈 采购金额趋势图
                <div class="chart-summary">
                  <div class="summary-item">
                    <span class="label">本月采购额</span>
                    <span class="value">¥580,000</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">环比增长</span>
                    <span class="value positive">+15.2%</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">同比增长</span>
                    <span class="value positive">+23.8%</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 采购方式分布 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>采购方式分布</h3>
                <el-button type="text" size="small">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                🥧 采购方式分布图
                <div class="distribution-stats">
                  <div class="stat-item">
                    <div class="stat-color" style="background: #667eea;"></div>
                    <span class="stat-label">公开招标</span>
                    <span class="stat-value">45%</span>
                  </div>
                  <div class="stat-item">
                    <div class="stat-color" style="background: #f093fb;"></div>
                    <span class="stat-label">竞争性磋商</span>
                    <span class="stat-value">30%</span>
                  </div>
                  <div class="stat-item">
                    <div class="stat-color" style="background: #4facfe;"></div>
                    <span class="stat-label">询价采购</span>
                    <span class="stat-value">20%</span>
                  </div>
                  <div class="stat-item">
                    <div class="stat-color" style="background: #43e97b;"></div>
                    <span class="stat-label">单一来源</span>
                    <span class="stat-value">5%</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="24" style="margin-top: 24px;">
        <!-- 部门采购统计 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>部门采购统计</h3>
                <el-button type="text" size="small">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                📊 部门采购统计图
                <div class="department-stats">
                  <div class="dept-item">
                    <span class="dept-name">信息中心</span>
                    <div class="dept-bar">
                      <div class="dept-progress" style="width: 85%;"></div>
                    </div>
                    <span class="dept-amount">¥680,000</span>
                  </div>
                  <div class="dept-item">
                    <span class="dept-name">行政部</span>
                    <div class="dept-bar">
                      <div class="dept-progress" style="width: 65%;"></div>
                    </div>
                    <span class="dept-amount">¥520,000</span>
                  </div>
                  <div class="dept-item">
                    <span class="dept-name">财务部</span>
                    <div class="dept-bar">
                      <div class="dept-progress" style="width: 45%;"></div>
                    </div>
                    <span class="dept-amount">¥360,000</span>
                  </div>
                  <div class="dept-item">
                    <span class="dept-name">业务部</span>
                    <div class="dept-bar">
                      <div class="dept-progress" style="width: 35%;"></div>
                    </div>
                    <span class="dept-amount">¥280,000</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 供应商绩效分析 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <h3>供应商绩效分析</h3>
                <el-button type="text" size="small">查看详情</el-button>
              </div>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                ⭐ 供应商绩效分析
                <div class="supplier-performance">
                  <div class="performance-item excellent">
                    <div class="performance-label">优秀</div>
                    <div class="performance-count">28</div>
                    <div class="performance-percent">31.5%</div>
                  </div>
                  <div class="performance-item good">
                    <div class="performance-label">良好</div>
                    <div class="performance-count">35</div>
                    <div class="performance-percent">39.3%</div>
                  </div>
                  <div class="performance-item average">
                    <div class="performance-label">一般</div>
                    <div class="performance-count">20</div>
                    <div class="performance-percent">22.5%</div>
                  </div>
                  <div class="performance-item poor">
                    <div class="performance-label">较差</div>
                    <div class="performance-count">6</div>
                    <div class="performance-percent">6.7%</div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 合规性分析 -->
    <div class="compliance-section">
      <el-card class="compliance-card">
        <template #header>
          <div class="card-header">
            <h3>合规性分析</h3>
            <el-button type="primary" size="small">生成报告</el-button>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="6">
            <div class="compliance-metric">
              <div class="metric-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-title">双人制执行率</div>
                <div class="metric-value">98.5%</div>
                <div class="metric-status excellent">优秀</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="compliance-metric">
              <div class="metric-icon">
                <el-icon><Select /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-title">多人验收率</div>
                <div class="metric-value">96.8%</div>
                <div class="metric-status excellent">优秀</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="compliance-metric">
              <div class="metric-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-title">按时付款率</div>
                <div class="metric-value">95.6%</div>
                <div class="metric-status good">良好</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="compliance-metric">
              <div class="metric-icon">
                <el-icon><DocumentChecked /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-title">文档完整率</div>
                <div class="metric-value">99.2%</div>
                <div class="metric-status excellent">优秀</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 时间范围
const timeRange = ref('30d')

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据刷新成功')
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.analytics-dashboard {
  padding: 24px;

  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;

    .header-content {
      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-icon {
          color: #10b981;
          font-size: 32px;
        }
      }

      .page-subtitle {
        font-size: 16px;
        color: #64748b;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }

  .metrics-section {
    margin-bottom: 32px;

    .metric-card {
      background: #fff;
      border-radius: 16px;
      padding: 24px;
      display: flex;
      align-items: center;
      transition: all 0.3s ease;
      border: 1px solid rgba(226, 232, 240, 0.8);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
      }

      .metric-icon {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;

        .el-icon {
          font-size: 28px;
          color: #fff;
        }
      }

      .metric-content {
        flex: 1;

        .metric-value {
          font-size: 32px;
          font-weight: 800;
          color: #1e293b;
          line-height: 1;
          margin-bottom: 8px;
        }

        .metric-label {
          font-size: 14px;
          color: #64748b;
          margin-bottom: 8px;
        }

        .metric-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;
          font-weight: 600;

          &.positive {
            color: #10b981;
          }

          &.negative {
            color: #ef4444;
          }

          .el-icon {
            font-size: 16px;
          }
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 32px;

    .chart-card {
      border-radius: 16px;
      border: 1px solid rgba(226, 232, 240, 0.8);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      }

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }
      }

      .chart-container {
        .chart-placeholder {
          height: 280px;
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-radius: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-size: 48px;
          color: #64748b;
          position: relative;

          .chart-summary {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-around;

            .summary-item {
              text-align: center;

              .label {
                display: block;
                font-size: 12px;
                color: #64748b;
                margin-bottom: 4px;
              }

              .value {
                display: block;
                font-size: 16px;
                font-weight: 600;
                color: #1e293b;

                &.positive {
                  color: #10b981;
                }
              }
            }
          }

          .distribution-stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;

            .stat-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              font-size: 14px;

              .stat-color {
                width: 12px;
                height: 12px;
                border-radius: 2px;
                margin-right: 8px;
              }

              .stat-label {
                flex: 1;
                color: #64748b;
              }

              .stat-value {
                font-weight: 600;
                color: #1e293b;
              }
            }
          }

          .department-stats {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;

            .dept-item {
              display: flex;
              align-items: center;
              margin-bottom: 12px;
              font-size: 14px;

              .dept-name {
                width: 80px;
                color: #64748b;
                font-size: 12px;
              }

              .dept-bar {
                flex: 1;
                height: 8px;
                background: #e2e8f0;
                border-radius: 4px;
                margin: 0 12px;
                overflow: hidden;

                .dept-progress {
                  height: 100%;
                  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                  border-radius: 4px;
                }
              }

              .dept-amount {
                width: 80px;
                text-align: right;
                font-weight: 600;
                color: #1e293b;
                font-size: 12px;
              }
            }
          }

          .supplier-performance {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;

            .performance-item {
              text-align: center;
              padding: 12px;
              border-radius: 8px;

              .performance-label {
                font-size: 12px;
                color: #64748b;
                margin-bottom: 4px;
              }

              .performance-count {
                font-size: 20px;
                font-weight: 700;
                margin-bottom: 2px;
              }

              .performance-percent {
                font-size: 12px;
                font-weight: 500;
              }

              &.excellent {
                background: rgba(16, 185, 129, 0.1);
                color: #10b981;
              }

              &.good {
                background: rgba(59, 130, 246, 0.1);
                color: #3b82f6;
              }

              &.average {
                background: rgba(245, 158, 11, 0.1);
                color: #f59e0b;
              }

              &.poor {
                background: rgba(239, 68, 68, 0.1);
                color: #ef4444;
              }
            }
          }
        }
      }
    }
  }

  .compliance-section {
    .compliance-card {
      border-radius: 16px;
      border: 1px solid rgba(226, 232, 240, 0.8);

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h3 {
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          margin: 0;
        }
      }

      .compliance-metric {
        display: flex;
        align-items: center;
        padding: 20px;
        background: #f8fafc;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          background: #f1f5f9;
          transform: translateY(-2px);
        }

        .metric-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .el-icon {
            font-size: 24px;
            color: #fff;
          }
        }

        .metric-info {
          .metric-title {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 4px;
          }

          .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
          }

          .metric-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 500;

            &.excellent {
              background: rgba(16, 185, 129, 0.1);
              color: #10b981;
            }

            &.good {
              background: rgba(59, 130, 246, 0.1);
              color: #3b82f6;
            }

            &.average {
              background: rgba(245, 158, 11, 0.1);
              color: #f59e0b;
            }
          }
        }
      }
    }
  }
}
</style>
